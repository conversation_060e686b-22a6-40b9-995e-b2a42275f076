// 'use client';

// import { EnvelopeIcon } from '@heroicons/react/24/solid';

// export default function NewsletterSection() {
//   return (
//     <section className="py-20 px-6 bg-black text-white text-center">
//       <h2 className="text-3xl font-bold mb-4">Join the Luxentra Club</h2>
//       <p className="mb-6 text-gray-300">
//         Be the first to know about exclusive launches, curated collections, and VIP-only offers.
//       </p>

//       <form className="flex flex-col sm:flex-row items-center justify-center gap-4 max-w-lg mx-auto">
//         <input
//           type="email"
//           placeholder="Enter your email"
//           className="w-full sm:w-auto flex-1 px-4 py-3 rounded-full text-black"
//         />
//         <button
//           type="submit"
//           className="flex items-center gap-2 bg-white text-black px-6 py-3 rounded-full hover:bg-gray-200 transition"
//         >
//           <EnvelopeIcon className="h-5 w-5" />
//           Subscribe
//         </button>
//       </form>
//     </section>
//   );
// }
